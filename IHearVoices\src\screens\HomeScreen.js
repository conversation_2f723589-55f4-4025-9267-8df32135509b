import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS, ROUTES } from '../utils/constants';
import ProductCard from '../components/ProductCard';
import VoiceButton from '../components/VoiceButton';
import TranscriptDisplay from '../components/TranscriptDisplay';
import productService from '../services/productService';
import { useVoice } from '../context/VoiceContext';
import { useCart } from '../context/CartContext';

const HomeScreen = ({ navigation }) => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  
  const { searchResults, speak } = useVoice();
  const { getItemCount } = useCart();
  
  // Load products and categories
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const allProducts = await productService.getProducts();
        const allCategories = await productService.getCategories();
        
        setProducts(allProducts);
        setCategories(allCategories);
        
        // Welcome message
        speak('Welcome to I Hear Voices. Say "search for" followed by a product name to start shopping.');
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, []);
  
  // Update products when search results change
  useEffect(() => {
    if (searchResults && searchResults.length > 0) {
      setProducts(searchResults);
      setSelectedCategory(null);
    }
  }, [searchResults]);
  
  // Filter products by category
  const filterByCategory = async (category) => {
    setLoading(true);
    
    try {
      if (category === selectedCategory) {
        // If the same category is selected again, show all products
        const allProducts = await productService.getProducts();
        setProducts(allProducts);
        setSelectedCategory(null);
      } else {
        // Filter by the selected category
        const filteredProducts = await productService.getProductsByCategory(category);
        setProducts(filteredProducts);
        setSelectedCategory(category);
      }
    } catch (error) {
      console.error('Error filtering products:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle search
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    
    try {
      const results = await productService.searchProducts(searchQuery);
      setProducts(results);
      setSelectedCategory(null);
    } catch (error) {
      console.error('Error searching products:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Navigate to cart
  const navigateToCart = () => {
    navigation.navigate(ROUTES.CART);
  };
  
  // Render category item
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.id && styles.selectedCategoryItem,
      ]}
      onPress={() => filterByCategory(item.id)}
    >
      <Text
        style={[
          styles.categoryText,
          selectedCategory === item.id && styles.selectedCategoryText,
        ]}
      >
        {item.name}
      </Text>
    </TouchableOpacity>
  );
  
  // Render product item
  const renderProductItem = ({ item }) => (
    <ProductCard product={item} />
  );
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.BACKGROUND} barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
          <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
            <MaterialIcons name="search" size={24} color={COLORS.PRIMARY} />
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity style={styles.cartButton} onPress={navigateToCart}>
          <MaterialIcons name="shopping-cart" size={24} color={COLORS.TEXT} />
          {getItemCount() > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{getItemCount()}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
      
      {/* Categories */}
      <View style={styles.categoriesContainer}>
        <FlatList
          data={categories}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesList}
        />
      </View>
      
      {/* Products */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.PRIMARY} />
        </View>
      ) : (
        <FlatList
          data={products}
          renderItem={renderProductItem}
          keyExtractor={(item) => item.id}
          numColumns={2}
          columnWrapperStyle={styles.productRow}
          contentContainerStyle={styles.productsList}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <MaterialIcons name="search-off" size={48} color={COLORS.LIGHT_TEXT} />
              <Text style={styles.emptyText}>No products found</Text>
            </View>
          }
        />
      )}
      
      {/* Voice Button */}
      <View style={styles.voiceButtonContainer}>
        <VoiceButton />
      </View>
      
      {/* Transcript Display */}
      <TranscriptDisplay />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  searchButton: {
    padding: 5,
  },
  cartButton: {
    marginLeft: 15,
    padding: 5,
  },
  badge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: COLORS.SECONDARY,
    borderRadius: 10,
    width: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  categoriesContainer: {
    marginVertical: 10,
  },
  categoriesList: {
    paddingHorizontal: 15,
  },
  categoryItem: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
  },
  selectedCategoryItem: {
    backgroundColor: COLORS.PRIMARY,
  },
  categoryText: {
    fontSize: 14,
    color: COLORS.TEXT,
  },
  selectedCategoryText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  productsList: {
    padding: 15,
  },
  productRow: {
    justifyContent: 'space-between',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
    marginTop: 10,
  },
  voiceButtonContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
});

export default HomeScreen; 
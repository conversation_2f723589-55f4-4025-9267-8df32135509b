import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS } from '../utils/constants';
import { useCart } from '../context/CartContext';

const CartItem = ({ item }) => {
  const { product, quantity } = item;
  const { updateItemQuantity, removeItem } = useCart();
  
  const handleIncrement = () => {
    updateItemQuantity(product.id, quantity + 1);
  };
  
  const handleDecrement = () => {
    if (quantity > 1) {
      updateItemQuantity(product.id, quantity - 1);
    }
  };
  
  const handleRemove = () => {
    removeItem(product.id);
  };
  
  return (
    <View style={styles.container}>
      <Image
        source={{ uri: product.imageUrl }}
        style={styles.image}
        resizeMode="cover"
      />
      
      <View style={styles.content}>
        <Text style={styles.name} numberOfLines={1}>{product.name}</Text>
        <Text style={styles.price}>{product.currency} {product.price.toFixed(2)}</Text>
        
        <View style={styles.actions}>
          <View style={styles.quantityContainer}>
            <TouchableOpacity
              style={[styles.quantityButton, quantity <= 1 && styles.quantityButtonDisabled]}
              onPress={handleDecrement}
              disabled={quantity <= 1}
            >
              <MaterialIcons name="remove" size={16} color={quantity <= 1 ? COLORS.LIGHT_TEXT : COLORS.TEXT} />
            </TouchableOpacity>
            
            <Text style={styles.quantity}>{quantity}</Text>
            
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={handleIncrement}
            >
              <MaterialIcons name="add" size={16} color={COLORS.TEXT} />
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity
            style={styles.removeButton}
            onPress={handleRemove}
          >
            <MaterialIcons name="delete-outline" size={20} color={COLORS.SECONDARY} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  image: {
    width: 80,
    height: 80,
  },
  content: {
    flex: 1,
    padding: 10,
    justifyContent: 'space-between',
  },
  name: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.TEXT,
    marginBottom: 4,
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 8,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 5,
    overflow: 'hidden',
  },
  quantityButton: {
    padding: 6,
    backgroundColor: '#F5F5F5',
  },
  quantityButtonDisabled: {
    backgroundColor: '#F0F0F0',
  },
  quantity: {
    paddingHorizontal: 12,
    fontSize: 14,
    fontWeight: '600',
  },
  removeButton: {
    padding: 6,
  },
});

export default CartItem; 
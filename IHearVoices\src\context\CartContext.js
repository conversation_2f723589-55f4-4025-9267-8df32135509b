import React, { createContext, useState, useEffect, useContext } from 'react';
import cartService from '../services/cartService';

// Create context
const CartContext = createContext();

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState({ items: [], total: 0 });
  const [loading, setLoading] = useState(true);

  // Load cart when component mounts
  useEffect(() => {
    const loadCart = async () => {
      setLoading(true);
      await cartService.loadCart();
      setCart(cartService.getCart());
      setLoading(false);
    };
    
    loadCart();
    
    // Add listener for cart changes
    const listenerIndex = cartService.addListener(handleCartChange);
    
    return () => {
      // Remove listener when component unmounts
      cartService.removeListener(listenerIndex);
    };
  }, []);
  
  // Handle cart changes
  const handleCartChange = (updatedCart) => {
    setCart(updatedCart);
  };
  
  // Add item to cart
  const addItem = async (product, quantity = 1) => {
    await cartService.addItem(product, quantity);
  };
  
  // Remove item from cart
  const removeItem = async (productId) => {
    await cartService.removeItem(productId);
  };
  
  // Update item quantity
  const updateItemQuantity = async (productId, quantity) => {
    await cartService.updateItemQuantity(productId, quantity);
  };
  
  // Clear cart
  const clearCart = async () => {
    await cartService.clearCart();
  };
  
  // Get item count
  const getItemCount = () => {
    return cart.items.reduce((total, item) => total + item.quantity, 0);
  };
  
  // Get cart total
  const getTotal = () => {
    return cart.total;
  };
  
  // Check if cart has items
  const hasItems = () => {
    return cart.items.length > 0;
  };
  
  // Context value
  const value = {
    cart,
    loading,
    addItem,
    removeItem,
    updateItemQuantity,
    clearCart,
    getItemCount,
    getTotal,
    hasItems,
  };
  
  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

// Custom hook to use cart context
export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export default CartContext; 
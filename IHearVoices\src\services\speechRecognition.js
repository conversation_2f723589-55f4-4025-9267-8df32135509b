import * as Speech from 'expo-speech';
import { Audio } from 'expo-av';
import { VOICE_COMMANDS } from '../utils/constants';

class SpeechRecognitionService {
  constructor() {
    this.isRecording = false;
    this.recording = null;
    this.listeners = [];
    
    // Add mock responses for development
    this.mockResponses = [
      "search for black shoes",
      "find blue jeans",
      "look for white t-shirt",
      "show me accessories",
      "add to cart",
      "view cart",
      "checkout",
      "confirm",
      "cancel"
    ];
    this.lastMockIndex = -1;
  }

  // Initialize the speech recognition service
  async init() {
    try {
      // Request permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Permission to access microphone was denied');
      }
      
      // Set up audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
      });
      
      return true;
    } catch (error) {
      console.error('Error initializing speech recognition:', error);
      return false;
    }
  }

  // Start recording audio
  async startRecording() {
    try {
      if (this.isRecording) {
        await this.stopRecording();
      }
      
      this.isRecording = true;
      
      // Create recording object
      const recording = new Audio.Recording();
      await recording.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);
      await recording.startAsync();
      this.recording = recording;
      
      // Notify listeners that recording started
      this._notifyListeners({ type: 'recording_started' });
      
      return true;
    } catch (error) {
      console.error('Error starting recording:', error);
      this.isRecording = false;
      return false;
    }
  }

  // Stop recording and process speech
  async stopRecording() {
    try {
      if (!this.isRecording || !this.recording) {
        return null;
      }
      
      this.isRecording = false;
      
      // Notify listeners that recording is being processed
      this._notifyListeners({ type: 'processing' });
      
      // Stop recording
      await this.recording.stopAndUnloadAsync();
      
      // Get recording URI
      const uri = this.recording.getURI();
      this.recording = null;
      
      // In a real implementation, we would send this audio file to a speech recognition API
      // For now, we'll simulate a response with a timeout and cycle through mock responses
      return new Promise((resolve) => {
        setTimeout(() => {
          // Get a different mock response each time
          this.lastMockIndex = (this.lastMockIndex + 1) % this.mockResponses.length;
          const mockTranscription = this.mockResponses[this.lastMockIndex];
          
          // Notify listeners with the transcription
          this._notifyListeners({ 
            type: 'transcription_complete', 
            transcription: mockTranscription 
          });
          
          resolve(mockTranscription);
        }, 1000);
      });
    } catch (error) {
      console.error('Error stopping recording:', error);
      this.isRecording = false;
      this._notifyListeners({ type: 'error', error });
      return null;
    }
  }

  // Parse the transcription to determine the command and parameters
  parseCommand(transcription) {
    if (!transcription) return null;
    
    const lowerText = transcription.toLowerCase();
    
    // Check for search commands
    for (const searchTerm of VOICE_COMMANDS.SEARCH) {
      if (lowerText.includes(searchTerm)) {
        const query = lowerText.split(searchTerm)[1].trim();
        return {
          type: 'search',
          query
        };
      }
    }
    
    // Check for add to cart commands
    for (const addTerm of VOICE_COMMANDS.ADD_TO_CART) {
      if (lowerText.includes(addTerm)) {
        // Extract product info if possible
        let productInfo = lowerText.split(addTerm)[1].trim();
        return {
          type: 'add_to_cart',
          productInfo
        };
      }
    }
    
    // Check for view cart commands
    for (const viewCartTerm of VOICE_COMMANDS.VIEW_CART) {
      if (lowerText.includes(viewCartTerm)) {
        return {
          type: 'view_cart'
        };
      }
    }
    
    // Check for checkout commands
    for (const checkoutTerm of VOICE_COMMANDS.CHECKOUT) {
      if (lowerText.includes(checkoutTerm)) {
        return {
          type: 'checkout'
        };
      }
    }
    
    // Check for confirmation commands
    for (const confirmTerm of VOICE_COMMANDS.CONFIRM) {
      if (lowerText === confirmTerm) {
        return {
          type: 'confirm'
        };
      }
    }
    
    // Check for cancel commands
    for (const cancelTerm of VOICE_COMMANDS.CANCEL) {
      if (lowerText === cancelTerm) {
        return {
          type: 'cancel'
        };
      }
    }
    
    // If no command is recognized
    return {
      type: 'unknown',
      text: transcription
    };
  }

  // Speak text back to the user
  async speak(text) {
    return await Speech.speak(text, {
      language: 'en-US',
      pitch: 1.0,
      rate: 0.9,
    });
  }

  // Add a listener for speech recognition events
  addListener(callback) {
    this.listeners.push(callback);
    return this.listeners.length - 1; // Return the index for removal
  }

  // Remove a listener
  removeListener(index) {
    if (index >= 0 && index < this.listeners.length) {
      this.listeners.splice(index, 1);
    }
  }

  // Notify all listeners of an event
  _notifyListeners(event) {
    this.listeners.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in speech recognition listener:', error);
      }
    });
  }
}

// Create and export a singleton instance
const speechRecognition = new SpeechRecognitionService();
export default speechRecognition; 
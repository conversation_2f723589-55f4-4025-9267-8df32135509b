import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { PaystackWebViewProps } from 'react-native-paystack-webview';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS, ROUTES } from '../utils/constants';
import VoiceButton from '../components/VoiceButton';
import TranscriptDisplay from '../components/TranscriptDisplay';
import { useVoice } from '../context/VoiceContext';
import paymentService from '../services/paymentService';

const PaymentScreen = ({ route, navigation }) => {
  const { customerInfo, amount } = route.params || {};
  const { speak } = useVoice();
  const [loading, setLoading] = useState(true);
  const [paymentConfig, setPaymentConfig] = useState(null);
  const paystackWebViewRef = useRef();
  
  // Prepare payment configuration
  useEffect(() => {
    const preparePayment = () => {
      try {
        if (!customerInfo || !amount) {
          throw new Error('Missing payment information');
        }
        
        // Get payment configuration
        const config = paymentService.getPaymentConfig(
          amount,
          customerInfo.email,
          handlePaymentCallback
        );
        
        setPaymentConfig(config);
        setLoading(false);
        
        // Announce payment instructions
        speak('You are about to make a payment. The total amount is ' + amount + ' Ghana cedis. Say "confirm payment" to proceed or "cancel" to go back.');
      } catch (error) {
        console.error('Error preparing payment:', error);
        Alert.alert(
          'Payment Error',
          'There was an error preparing your payment. Please try again.',
          [
            {
              text: 'Go Back',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      }
    };
    
    preparePayment();
  }, [customerInfo, amount]);
  
  // Handle payment callback
  const handlePaymentCallback = (response) => {
    if (response.success) {
      // Payment successful
      navigation.navigate(ROUTES.SUCCESS, {
        order: response.order,
      });
    } else if (response.cancelled) {
      // Payment cancelled
      Alert.alert(
        'Payment Cancelled',
        'Your payment was cancelled.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } else {
      // Payment failed
      Alert.alert(
        'Payment Failed',
        response.message || 'There was an error processing your payment.',
        [
          {
            text: 'Try Again',
            onPress: () => paystackWebViewRef.current?.startTransaction(),
          },
          {
            text: 'Go Back',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    }
  };
  
  // Handle cancel payment
  const handleCancelPayment = () => {
    Alert.alert(
      'Cancel Payment',
      'Are you sure you want to cancel this payment?',
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes',
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };
  
  // Handle confirm payment via voice
  const handleConfirmPayment = () => {
    if (paystackWebViewRef.current) {
      paystackWebViewRef.current.startTransaction();
    }
  };
  
  return (
    <SafeAreaView style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.PRIMARY} />
          <Text style={styles.loadingText}>Preparing payment...</Text>
        </View>
      ) : (
        <>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Payment</Text>
          </View>
          
          <View style={styles.paymentInfoContainer}>
            <Text style={styles.paymentInfoTitle}>Payment Summary</Text>
            
            <View style={styles.paymentInfoRow}>
              <Text style={styles.paymentInfoLabel}>Amount</Text>
              <Text style={styles.paymentInfoValue}>GHS {amount.toFixed(2)}</Text>
            </View>
            
            <View style={styles.paymentInfoRow}>
              <Text style={styles.paymentInfoLabel}>Payment Method</Text>
              <Text style={styles.paymentInfoValue}>Paystack</Text>
            </View>
            
            <View style={styles.paymentInfoRow}>
              <Text style={styles.paymentInfoLabel}>Email</Text>
              <Text style={styles.paymentInfoValue}>{customerInfo.email}</Text>
            </View>
          </View>
          
          <View style={styles.instructionsContainer}>
            <Text style={styles.instructionsTitle}>Instructions</Text>
            <Text style={styles.instructionsText}>
              1. Click "Proceed to Payment" to start the payment process.
            </Text>
            <Text style={styles.instructionsText}>
              2. You will be redirected to Paystack to complete your payment.
            </Text>
            <Text style={styles.instructionsText}>
              3. After successful payment, you will return to the app.
            </Text>
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancelPayment}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.payButton}
              onPress={handleConfirmPayment}
            >
              <Text style={styles.payButtonText}>Proceed to Payment</Text>
              <MaterialIcons name="payment" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
          
          {/* Hidden Paystack WebView */}
          {paymentConfig && (
            <View style={styles.hiddenWebView}>
              <PaystackWebView
                ref={paystackWebViewRef}
                buttonText="Pay Now"
                showPayButton={false}
                paystackKey={paymentConfig.publicKey}
                amount={paymentConfig.amount}
                billingEmail={paymentConfig.email}
                billingMobile={customerInfo.phone}
                billingName={customerInfo.name}
                ActivityIndicatorColor={COLORS.PRIMARY}
                SafeAreaViewContainer={{ display: 'none' }}
                SafeAreaViewContainerModal={{ display: 'none' }}
                onCancel={paymentConfig.onCancel}
                onSuccess={paymentConfig.onSuccess}
                autoStart={false}
                refNumber={paymentConfig.reference}
                channels={paymentConfig.channels}
                currency={paymentConfig.currency}
                metadata={paymentConfig.metadata}
              />
            </View>
          )}
        </>
      )}
      
      {/* Voice Button */}
      <View style={styles.voiceButtonContainer}>
        <VoiceButton />
      </View>
      
      {/* Transcript Display */}
      <TranscriptDisplay />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: COLORS.TEXT,
  },
  header: {
    paddingHorizontal: 15,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT,
  },
  paymentInfoContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    margin: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  paymentInfoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 15,
  },
  paymentInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  paymentInfoLabel: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
  },
  paymentInfoValue: {
    fontSize: 16,
    color: COLORS.TEXT,
    fontWeight: '500',
  },
  instructionsContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    margin: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  instructionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 15,
  },
  instructionsText: {
    fontSize: 16,
    color: COLORS.TEXT,
    marginBottom: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    marginTop: 'auto',
  },
  cancelButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    marginRight: 10,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 5,
  },
  cancelButtonText: {
    fontSize: 16,
    color: COLORS.TEXT,
  },
  payButton: {
    flex: 2,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 12,
    borderRadius: 5,
  },
  payButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 5,
  },
  hiddenWebView: {
    position: 'absolute',
    width: 0,
    height: 0,
    opacity: 0,
  },
  voiceButtonContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
});

export default PaymentScreen; 
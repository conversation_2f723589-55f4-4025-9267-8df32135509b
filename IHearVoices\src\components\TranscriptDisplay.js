import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { useVoice } from '../context/VoiceContext';
import { COLORS } from '../utils/constants';

const TranscriptDisplay = () => {
  const { transcript, status } = useVoice();
  const [visible, setVisible] = useState(false);
  const fadeAnim = useState(new Animated.Value(0))[0];
  
  useEffect(() => {
    if (transcript && (status === 'processing' || status === 'speaking')) {
      setVisible(true);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
      
      // Hide after 3 seconds
      const timer = setTimeout(() => {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start(() => {
          setVisible(false);
        });
      }, 3000);
      
      return () => clearTimeout(timer);
    } else if (status === 'listening') {
      setVisible(true);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setVisible(false);
      });
    }
  }, [transcript, status]);
  
  if (!visible) return null;
  
  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.bubble}>
        {status === 'listening' ? (
          <Text style={styles.listeningText}>Listening...</Text>
        ) : (
          <Text style={styles.transcriptText}>{transcript}</Text>
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 20,
    alignItems: 'center',
    zIndex: 10,
  },
  bubble: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    maxWidth: '80%',
  },
  listeningText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  transcriptText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
});

export default TranscriptDisplay; 
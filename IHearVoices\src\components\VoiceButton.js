import React from 'react';
import { TouchableOpacity, StyleSheet, View, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useVoice } from '../context/VoiceContext';
import { COLORS } from '../utils/constants';

const VoiceButton = ({ size = 60, style }) => {
  const { status, startListening, stopListening } = useVoice();
  
  const isListening = status === 'listening';
  const isProcessing = status === 'processing';
  const isSpeaking = status === 'speaking';
  const isActive = isListening || isProcessing || isSpeaking;
  
  const handlePress = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        { width: size, height: size, borderRadius: size / 2 },
        isActive && styles.active,
        style
      ]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      {isProcessing ? (
        <ActivityIndicator color="#FFFFFF" size="small" />
      ) : (
        <MaterialIcons
          name={isListening ? 'mic' : isSpeaking ? 'volume-up' : 'mic-none'}
          size={size / 2}
          color="#FFFFFF"
        />
      )}
      
      {isListening && (
        <View style={styles.ripple} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  active: {
    backgroundColor: COLORS.SECONDARY,
  },
  ripple: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    transform: [{ scale: 1.2 }],
  },
});

export default VoiceButton; 
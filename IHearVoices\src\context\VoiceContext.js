import React, { createContext, useState, useEffect, useContext } from 'react';
import { Alert } from 'react-native';
import speechRecognition from '../services/speechRecognition';
import productService from '../services/productService';
import cartService from '../services/cartService';
import { useNavigation } from '@react-navigation/native';
import { ROUTES } from '../utils/constants';

// Create context
const VoiceContext = createContext();

// Voice command status
const VOICE_STATUS = {
  IDLE: 'idle',
  LISTENING: 'listening',
  PROCESSING: 'processing',
  SPEAKING: 'speaking',
};

export const VoiceProvider = ({ children }) => {
  const [status, setStatus] = useState(VOICE_STATUS.IDLE);
  const [isEnabled, setIsEnabled] = useState(true);
  const [transcript, setTranscript] = useState('');
  const [lastCommand, setLastCommand] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const navigation = useNavigation();

  // Initialize speech recognition
  useEffect(() => {
    const initSpeech = async () => {
      const initialized = await speechRecognition.init();
      if (!initialized) {
        Alert.alert(
          'Microphone Permission',
          'Please grant microphone permission to use voice commands.',
          [{ text: 'OK' }]
        );
        setIsEnabled(false);
      }
    };
    
    initSpeech();
    
    // Add listener for speech recognition events
    const listenerIndex = speechRecognition.addListener(handleSpeechEvent);
    
    return () => {
      // Remove listener when component unmounts
      speechRecognition.removeListener(listenerIndex);
    };
  }, []);
  
  // Handle speech recognition events
  const handleSpeechEvent = async (event) => {
    switch (event.type) {
      case 'recording_started':
        setStatus(VOICE_STATUS.LISTENING);
        break;
        
      case 'processing':
        setStatus(VOICE_STATUS.PROCESSING);
        break;
        
      case 'transcription_complete':
        setTranscript(event.transcription);
        
        // Parse command
        const command = speechRecognition.parseCommand(event.transcription);
        setLastCommand(command);
        
        // Process command
        await processCommand(command);
        break;
        
      case 'error':
        setStatus(VOICE_STATUS.IDLE);
        Alert.alert('Voice Recognition Error', 'There was an error processing your voice command.');
        break;
    }
  };
  
  // Process voice command
  const processCommand = async (command) => {
    if (!command) return;
    
    switch (command.type) {
      case 'search':
        setStatus(VOICE_STATUS.SPEAKING);
        await speechRecognition.speak(`Searching for ${command.query}`);
        
        // Search for products
        const results = await productService.searchProducts(command.query);
        setSearchResults(results);
        
        // Navigate to search results
        navigation.navigate(ROUTES.SEARCH, { query: command.query, results });
        
        setStatus(VOICE_STATUS.IDLE);
        break;
        
      case 'add_to_cart':
        if (selectedProduct) {
          setStatus(VOICE_STATUS.SPEAKING);
          await speechRecognition.speak(`Adding ${selectedProduct.name} to cart`);
          
          // Add product to cart
          await cartService.addItem(selectedProduct);
          
          setStatus(VOICE_STATUS.IDLE);
        } else {
          setStatus(VOICE_STATUS.SPEAKING);
          await speechRecognition.speak('Please select a product first');
          setStatus(VOICE_STATUS.IDLE);
        }
        break;
        
      case 'view_cart':
        setStatus(VOICE_STATUS.SPEAKING);
        
        const cart = cartService.getCart();
        if (cart.items.length === 0) {
          await speechRecognition.speak('Your cart is empty');
        } else {
          await speechRecognition.speak(`You have ${cart.items.length} items in your cart`);
          // Navigate to cart
          navigation.navigate(ROUTES.CART);
        }
        
        setStatus(VOICE_STATUS.IDLE);
        break;
        
      case 'checkout':
        setStatus(VOICE_STATUS.SPEAKING);
        
        const checkoutCart = cartService.getCart();
        if (checkoutCart.items.length === 0) {
          await speechRecognition.speak('Your cart is empty. Add items before checkout.');
        } else {
          await speechRecognition.speak('Proceeding to checkout');
          // Navigate to checkout
          navigation.navigate(ROUTES.CHECKOUT);
        }
        
        setStatus(VOICE_STATUS.IDLE);
        break;
        
      case 'confirm':
        // This will depend on the current screen/context
        // Implementation will vary based on where the user is in the app
        break;
        
      case 'cancel':
        setStatus(VOICE_STATUS.SPEAKING);
        await speechRecognition.speak('Operation cancelled');
        setStatus(VOICE_STATUS.IDLE);
        break;
        
      case 'unknown':
        setStatus(VOICE_STATUS.SPEAKING);
        await speechRecognition.speak('Sorry, I did not understand that command');
        setStatus(VOICE_STATUS.IDLE);
        break;
    }
  };
  
  // Start listening for voice commands
  const startListening = async () => {
    if (!isEnabled) {
      Alert.alert(
        'Voice Commands Disabled',
        'Voice commands are currently disabled. Please check microphone permissions.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    if (status === VOICE_STATUS.IDLE) {
      await speechRecognition.startRecording();
    }
  };
  
  // Stop listening for voice commands
  const stopListening = async () => {
    if (status === VOICE_STATUS.LISTENING) {
      await speechRecognition.stopRecording();
    }
  };
  
  // Speak a message
  const speak = async (message) => {
    setStatus(VOICE_STATUS.SPEAKING);
    await speechRecognition.speak(message);
    setStatus(VOICE_STATUS.IDLE);
  };
  
  // Select a product
  const selectProduct = (product) => {
    setSelectedProduct(product);
  };
  
  // Context value
  const value = {
    status,
    isEnabled,
    transcript,
    lastCommand,
    searchResults,
    selectedProduct,
    startListening,
    stopListening,
    speak,
    selectProduct,
  };
  
  return <VoiceContext.Provider value={value}>{children}</VoiceContext.Provider>;
};

// Custom hook to use voice context
export const useVoice = () => {
  const context = useContext(VoiceContext);
  if (!context) {
    throw new Error('useVoice must be used within a VoiceProvider');
  }
  return context;
};

export default VoiceContext; 
import AsyncStorage from '@react-native-async-storage/async-storage';

// In a real app, this would use Firebase Auth or another authentication provider
const USER_STORAGE_KEY = '@ihearvoices_user';

// Mock user data for development
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'password123', // In a real app, passwords would be hashed and not stored in code
    name: 'Test User',
    phone: '+233123456789',
    address: {
      street: '123 Main St',
      city: 'Accra',
      region: 'Greater Accra',
      country: 'Ghana',
    },
  },
];

class AuthService {
  constructor() {
    this.currentUser = null;
    this.listeners = [];
    
    // Load user from storage when service is initialized
    this.loadUser();
  }
  
  // Load user from AsyncStorage
  async loadUser() {
    try {
      const userData = await AsyncStorage.getItem(USER_STORAGE_KEY);
      if (userData) {
        this.currentUser = JSON.parse(userData);
        this._notifyListeners();
      }
      return this.currentUser;
    } catch (error) {
      console.error('Error loading user from storage:', error);
      return null;
    }
  }
  
  // Save user to AsyncStorage
  async _saveUser(user) {
    try {
      if (user) {
        await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
      } else {
        await AsyncStorage.removeItem(USER_STORAGE_KEY);
      }
    } catch (error) {
      console.error('Error saving user to storage:', error);
    }
  }
  
  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }
  
  // Check if user is logged in
  isLoggedIn() {
    return !!this.currentUser;
  }
  
  // Login with email and password
  async login(email, password) {
    try {
      // In a real app, this would be an API call to authenticate
      // For now, use mock data
      const user = mockUsers.find(
        u => u.email.toLowerCase() === email.toLowerCase() && u.password === password
      );
      
      if (!user) {
        throw new Error('Invalid email or password');
      }
      
      // Don't store password in memory or local storage
      const { password: _, ...userWithoutPassword } = user;
      this.currentUser = userWithoutPassword;
      
      // Save to storage and notify listeners
      await this._saveUser(this.currentUser);
      this._notifyListeners();
      
      return this.currentUser;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }
  
  // Register a new user
  async register(userData) {
    try {
      // In a real app, this would be an API call to create a user
      // For now, simulate registration
      
      // Check if email already exists
      const existingUser = mockUsers.find(
        u => u.email.toLowerCase() === userData.email.toLowerCase()
      );
      
      if (existingUser) {
        throw new Error('Email already in use');
      }
      
      // Create new user
      const newUser = {
        id: `${mockUsers.length + 1}`, // In a real app, this would be generated by the server
        ...userData,
      };
      
      // Add to mock data (in a real app, this would be saved to a database)
      mockUsers.push(newUser);
      
      // Don't store password in memory or local storage
      const { password: _, ...userWithoutPassword } = newUser;
      this.currentUser = userWithoutPassword;
      
      // Save to storage and notify listeners
      await this._saveUser(this.currentUser);
      this._notifyListeners();
      
      return this.currentUser;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }
  
  // Logout current user
  async logout() {
    this.currentUser = null;
    
    // Remove from storage and notify listeners
    await this._saveUser(null);
    this._notifyListeners();
  }
  
  // Update user profile
  async updateProfile(userData) {
    try {
      if (!this.currentUser) {
        throw new Error('User not logged in');
      }
      
      // In a real app, this would be an API call to update the user
      // For now, update mock data
      const userIndex = mockUsers.findIndex(u => u.id === this.currentUser.id);
      
      if (userIndex >= 0) {
        // Update user data
        mockUsers[userIndex] = {
          ...mockUsers[userIndex],
          ...userData,
        };
        
        // Don't store password in memory or local storage
        const { password: _, ...userWithoutPassword } = mockUsers[userIndex];
        this.currentUser = userWithoutPassword;
        
        // Save to storage and notify listeners
        await this._saveUser(this.currentUser);
        this._notifyListeners();
      }
      
      return this.currentUser;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }
  
  // Add a listener for auth changes
  addListener(callback) {
    this.listeners.push(callback);
    return this.listeners.length - 1; // Return the index for removal
  }
  
  // Remove a listener
  removeListener(index) {
    if (index >= 0 && index < this.listeners.length) {
      this.listeners.splice(index, 1);
    }
  }
  
  // Notify all listeners of auth changes
  _notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.currentUser);
      } catch (error) {
        console.error('Error in auth listener:', error);
      }
    });
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService; 